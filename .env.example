# Environment Configuration for Avyan AI Chatbot

# =============================================================================
# API Keys and External Services
# =============================================================================

# OpenAI API (for embeddings and fallback)
OPENAI_API_KEY=your_openai_api_key_here

# Deepgram API (Speech-to-Text)
DEEPGRAM_API_KEY=your_deepgram_api_key_here

# ElevenLabs API (Text-to-Speech)
ELEVENLABS_API_KEY=your_elevenlabs_api_key_here

# Hugging Face API (for model downloads)
HUGGINGFACE_API_TOKEN=your_huggingface_token_here

# LangSmith (for monitoring and tracing)
LANGCHAIN_API_KEY=your_langsmith_api_key_here
LANGCHAIN_TRACING_V2=true
LANGCHAIN_PROJECT=avyan-chatbot

# =============================================================================
# Database Configuration
# =============================================================================

# Qdrant Vector Database
QDRANT_HOST=localhost
QDRANT_PORT=6333
QDRANT_API_KEY=your_qdrant_api_key_here
QDRANT_COLLECTION_NAME=avyan_knowledge

# PostgreSQL (for conversation history and metadata)
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=avyan_chatbot
POSTGRES_USER=postgres
POSTGRES_PASSWORD=your_postgres_password_here

# Redis (for caching and session management)
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password_here

# =============================================================================
# Application Configuration
# =============================================================================

# Environment
ENVIRONMENT=development
DEBUG=true
LOG_LEVEL=INFO

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=1

# CORS Configuration
CORS_ORIGINS=["http://localhost:3000", "http://localhost:8080"]

# Security
SECRET_KEY=your_secret_key_here_change_in_production
ACCESS_TOKEN_EXPIRE_MINUTES=30

# =============================================================================
# AI Model Configuration
# =============================================================================

# Base LLM Model
BASE_MODEL_NAME=meta-llama/Llama-3.1-8B-Instruct
MODEL_CACHE_DIR=./models
DEVICE=cuda  # or cpu

# Fine-tuning Configuration
FINE_TUNED_MODEL_PATH=./ai-core/fine-tuning/models/avyan-llama-3.1-8b
LORA_ADAPTER_PATH=./ai-core/fine-tuning/adapters/avyan-lora

# Embedding Model
EMBEDDING_MODEL_NAME=sentence-transformers/all-MiniLM-L6-v2
EMBEDDING_DIMENSION=384

# RAG Configuration
CHUNK_SIZE=1000
CHUNK_OVERLAP=200
MAX_RETRIEVED_DOCS=5
SIMILARITY_THRESHOLD=0.7

# =============================================================================
# Voice Configuration
# =============================================================================

# Speech-to-Text
STT_LANGUAGE=en-US
STT_MODEL=nova-2

# Text-to-Speech
TTS_VOICE_ID=your_elevenlabs_voice_id_here
TTS_MODEL_ID=eleven_monolingual_v1
TTS_STABILITY=0.5
TTS_SIMILARITY_BOOST=0.8

# Audio Processing
AUDIO_SAMPLE_RATE=16000
AUDIO_CHUNK_SIZE=1024

# =============================================================================
# Performance and Scaling
# =============================================================================

# Request Limits
MAX_TOKENS_PER_REQUEST=2048
MAX_REQUESTS_PER_MINUTE=60
MAX_CONCURRENT_REQUESTS=10

# Caching
CACHE_TTL_SECONDS=3600
ENABLE_RESPONSE_CACHING=true

# Model Loading
LAZY_LOADING=true
MODEL_WARMUP=false

# =============================================================================
# Monitoring and Observability
# =============================================================================

# Metrics
ENABLE_METRICS=true
METRICS_PORT=9090

# Health Checks
HEALTH_CHECK_INTERVAL=30

# Logging
LOG_FORMAT=json
LOG_FILE=logs/avyan-chatbot.log

# =============================================================================
# Development and Testing
# =============================================================================

# Testing
TEST_DATABASE_URL=sqlite:///./test.db
MOCK_EXTERNAL_APIS=false

# Development
RELOAD=true
ENABLE_DOCS=true
ENABLE_REDOC=true

# =============================================================================
# Deployment Configuration
# =============================================================================

# Docker
DOCKER_REGISTRY=your_registry_here
IMAGE_TAG=latest

# Kubernetes
NAMESPACE=avyan-chatbot
REPLICAS=3

# Load Balancer
ENABLE_LOAD_BALANCER=true
LOAD_BALANCER_TYPE=nginx
