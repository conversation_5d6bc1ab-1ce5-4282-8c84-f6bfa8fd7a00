# Avyan AI Multi-Modal Chatbot

A sophisticated multi-modal AI chatbot showcasing Avyan AI Consulting's advanced capabilities through RAG (Retrieval-Augmented Generation), LLM fine-tuning, and real-time voice interaction.

## 🚀 Features

- **Multi-Modal Interaction**: Text and voice input/output
- **RAG System**: Real-time knowledge retrieval from curated database
- **Fine-Tuned LLM**: Custom Llama-3.1-8B model trained on Avyan AI's voice
- **Real-Time Streaming**: Low-latency voice and text responses
- **Scalable Architecture**: Production-ready deployment with monitoring

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   API Gateway   │    │   AI Core       │
│   Chat Widget   │◄──►│   (FastAPI)     │◄──►│   (RAG + LLM)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                        │
                       ┌─────────────────┐    ┌─────────────────┐
                       │   STT/TTS       │    │   Vector DB     │
                       │   Services      │    │   (Qdrant)      │
                       └─────────────────┘    └─────────────────┘
```

## 📁 Project Structure

```
avyan-chatbot/
├── backend/                 # FastAPI backend
│   ├── app/
│   │   ├── api/            # API routes
│   │   ├── core/           # Core configurations
│   │   ├── models/         # Data models
│   │   ├── services/       # Business logic
│   │   └── utils/          # Utilities
│   ├── tests/              # Backend tests
│   └── requirements.txt    # Python dependencies
├── frontend/               # React chat widget
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── hooks/          # Custom hooks
│   │   ├── services/       # API services
│   │   └── utils/          # Utilities
│   ├── public/             # Static assets
│   └── package.json        # Node dependencies
├── ai-core/                # AI/ML components
│   ├── rag/                # RAG implementation
│   ├── fine-tuning/        # Model fine-tuning
│   ├── embeddings/         # Embedding models
│   └── data/               # Training data
├── knowledge-base/         # Document storage
│   ├── documents/          # Source documents
│   ├── processed/          # Processed chunks
│   └── scripts/            # Processing scripts
├── deployment/             # Deployment configs
│   ├── docker/             # Docker files
│   ├── k8s/                # Kubernetes manifests
│   └── terraform/          # Infrastructure as code
├── docs/                   # Documentation
└── scripts/                # Utility scripts
```

## 🛠️ Technology Stack

| Component | Technology | Purpose |
|-----------|------------|---------|
| **Backend** | FastAPI | High-performance API with async support |
| **Frontend** | React + TypeScript | Modern, responsive chat interface |
| **LLM** | Llama-3.1-8B | Base model for fine-tuning |
| **Vector DB** | Qdrant | Fast similarity search for RAG |
| **STT** | Deepgram | Real-time speech recognition |
| **TTS** | ElevenLabs | Natural voice synthesis |
| **Orchestration** | LangChain + LlamaIndex | AI workflow management |
| **Fine-tuning** | QLoRA + PEFT | Efficient model adaptation |

## 🚀 Quick Start

### Prerequisites

- Python 3.9+
- Node.js 18+
- Docker & Docker Compose
- CUDA-compatible GPU (for fine-tuning)

### Installation

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd avyan-chatbot
   ```

2. **Set up backend**
   ```bash
   cd backend
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

3. **Set up frontend**
   ```bash
   cd frontend
   npm install
   ```

4. **Configure environment**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys and configurations
   ```

5. **Start services**
   ```bash
   docker-compose up -d  # Start Qdrant and other services
   cd backend && uvicorn app.main:app --reload
   cd frontend && npm start
   ```

## 📚 Documentation

- [API Documentation](docs/api.md)
- [Deployment Guide](docs/deployment.md)
- [Fine-tuning Guide](docs/fine-tuning.md)
- [Contributing Guidelines](docs/contributing.md)

## 🔧 Development

### Running Tests

```bash
# Backend tests
cd backend && pytest

# Frontend tests
cd frontend && npm test
```

### Code Quality

```bash
# Python formatting
black backend/
isort backend/

# TypeScript/JavaScript formatting
cd frontend && npm run lint
```

## 🚀 Deployment

The application supports multiple deployment options:

- **Development**: Docker Compose
- **Production**: Kubernetes with Helm charts
- **Cloud**: AWS EKS, GCP GKE, Azure AKS

See [Deployment Guide](docs/deployment.md) for detailed instructions.

## 📊 Monitoring

- **Metrics**: Prometheus + Grafana
- **Logging**: Structured logging with correlation IDs
- **Tracing**: LangSmith for AI workflow visibility
- **Health Checks**: Built-in health endpoints

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## 📄 License

This project is proprietary to Avyan AI Consulting.

## 🆘 Support

For support and questions, contact the Avyan AI team.

---

**Built with ❤️ by Avyan AI Consulting**
